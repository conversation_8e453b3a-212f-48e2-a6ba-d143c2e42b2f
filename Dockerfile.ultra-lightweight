# Ultra-lightweight Docker build - removes Chromium and heavy dependencies
FROM node:20-alpine AS builder

# Install minimal build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package.json package-lock.json ./

# Install dependencies and remove heavy packages
RUN npm ci --omit=dev --no-audit --no-fund && \
    npm uninstall pdf2pic gm puppeteer && \
    npm cache clean --force && \
    rm -rf /root/.npm

# Production stage - ultra lightweight
FROM node:20-alpine AS production

# Install only essential runtime dependencies (no Chromium)
RUN apk add --no-cache \
    dumb-init \
    && rm -rf /var/cache/apk/* \
    && rm -rf /tmp/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Set environment variables
ENV NODE_ENV=production

# Set working directory
WORKDIR /app

# Copy node_modules from builder stage
COPY --from=builder --chown=nodejs:nodejs /app/node_modules ./node_modules

# Copy application source
COPY --chown=nodejs:nodejs src ./src
COPY --chown=nodejs:nodejs .env.production ./.env

# Switch to non-root user
USER nodejs

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "src/index.js"]
