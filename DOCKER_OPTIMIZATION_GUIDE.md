# Docker Image Size Optimization Guide

## Current Situation
- **Original image size**: ~1.1GB
- **Current optimized size**: ~1.22GB
- **Target**: <500MB

## Main Size Contributors

### 1. System Packages (~883MB)
- **GraphicsMagick**: ~400MB
- **Ghostscript**: ~200MB  
- **Chromium**: ~283MB

### 2. Node Dependencies (~205MB)
- **pdf2pic + gm**: Heavy PDF processing
- **puppeteer**: Includes Chromium (but we use system Chromium)
- **sharp**: Image processing (~50MB)
- **LangChain packages**: ~80MB

## Optimization Strategies

### Strategy 1: Current Optimized Dockerfile (1.22GB)
**File**: `Dockerfile`
- ✅ Multi-stage build
- ✅ Production-only dependencies
- ✅ System package cleanup
- ✅ Non-root user
- ❌ Still includes heavy GraphicsMagick/Ghostscript

### Strategy 2: Lightweight Dockerfile (<400MB)
**File**: `Dockerfile.lightweight`
- ✅ Removes GraphicsMagick and Ghostscript
- ✅ Removes pdf2pic dependency
- ✅ Uses simplified PDF processing
- ⚠️ Requires code changes (see below)

### Strategy 3: Ultra-Lightweight with Alternative Dependencies
- Replace `pdf2pic` with `poppler-utils` (much smaller)
- Use `canvas` instead of `sharp` for basic image processing
- Consider `pdfjs-dist` for PDF text extraction

## Implementation Options

### Option A: Use Lightweight Dockerfile (Recommended)

1. **Replace the current Dockerfile**:
   ```bash
   mv Dockerfile Dockerfile.original
   mv Dockerfile.lightweight Dockerfile
   ```

2. **Update FileProcessingService**:
   ```bash
   mv src/services/FileProcessingService.js src/services/FileProcessingService.original.js
   mv src/services/FileProcessingService.lightweight.js src/services/FileProcessingService.js
   ```

3. **Remove heavy dependencies**:
   ```bash
   npm uninstall pdf2pic gm
   ```

4. **Build the optimized image**:
   ```bash
   docker build -t theinfini-ai-backend:lightweight .
   ```

**Expected size**: ~300-400MB

### Option B: Keep Current Functionality with Optimizations

1. **Use the current optimized Dockerfile** (already implemented)
2. **Consider dependency alternatives**:
   - Replace `pdf2pic` with `pdf-poppler` (lighter)
   - Use `jimp` instead of `sharp` for basic image processing
   - Implement lazy loading for heavy dependencies

### Option C: Hybrid Approach

1. **Create feature flags** for PDF processing modes
2. **Use lightweight processing by default**
3. **Enable heavy processing only when needed**

## Code Changes Required for Lightweight Version

### 1. Update package.json
Remove or replace heavy dependencies:
```json
{
  "dependencies": {
    // Remove these:
    // "pdf2pic": "^3.2.0",
    // "gm": "^1.25.1",
    
    // Keep these:
    "pdf-lib": "^1.17.1",
    "sharp": "^0.34.2",
    "mammoth": "^1.9.1"
  }
}
```

### 2. Update FileProcessingService
The lightweight version:
- ✅ Processes images with `sharp`
- ✅ Extracts basic PDF metadata with `pdf-lib`
- ✅ Processes Word/Excel documents normally
- ❌ No PDF-to-image conversion
- ⚠️ Simplified PDF content extraction

### 3. Update Frontend (if needed)
- Handle simplified PDF processing responses
- Show appropriate messages for PDF files
- Consider adding "enhanced processing" option

## Testing the Optimizations

### Build and Test Lightweight Version
```bash
# Build lightweight image
docker build -f Dockerfile.lightweight -t theinfini-ai-backend:lightweight .

# Check size
docker images theinfini-ai-backend:lightweight

# Test functionality
docker run -p 3000:3000 theinfini-ai-backend:lightweight
```

### Compare Sizes
```bash
docker images | grep theinfini-ai-backend
```

## Additional Optimizations

### 1. Use Alpine-based Node Image Alternatives
- Consider `node:20-alpine` variants
- Use `distroless` images for production

### 2. Optimize Node Dependencies
```bash
# Analyze bundle size
npm install -g webpack-bundle-analyzer
npx webpack-bundle-analyzer node_modules

# Remove unused dependencies
npm prune --production
```

### 3. Use .dockerignore Effectively
Already optimized in current version.

### 4. Consider External Services
- Move PDF processing to external service
- Use cloud-based image processing
- Implement microservices architecture

## Recommendations

### For Immediate Size Reduction (Recommended)
1. **Use Dockerfile.lightweight**
2. **Replace FileProcessingService with lightweight version**
3. **Remove pdf2pic and gm dependencies**
4. **Expected reduction**: 60-70% (from 1.1GB to ~350MB)

### For Maintaining Full Functionality
1. **Keep current optimized Dockerfile**
2. **Consider replacing pdf2pic with lighter alternatives**
3. **Implement lazy loading for heavy operations**
4. **Expected reduction**: 10-20% (from 1.1GB to ~900MB)

### For Production Deployment
1. **Use lightweight version for most deployments**
2. **Create separate "enhanced" image for advanced PDF processing**
3. **Use feature flags to control processing modes**
4. **Implement horizontal scaling for heavy operations**

## Next Steps

1. **Choose optimization strategy** based on requirements
2. **Test lightweight version** with your use cases
3. **Update deployment scripts** if needed
4. **Monitor performance** and functionality
5. **Consider gradual migration** if maintaining compatibility is important
