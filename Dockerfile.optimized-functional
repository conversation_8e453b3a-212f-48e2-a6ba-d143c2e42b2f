# Optimized Docker build with lightweight PDF generation
# Maintains all functionality while keeping image size reasonable
FROM node:20-alpine AS builder

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    pkgconfig \
    cairo-dev \
    pango-dev \
    libjpeg-turbo-dev \
    giflib-dev

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package.json package-lock.json ./

# Install dependencies and replace heavy packages with lighter alternatives
RUN npm ci --omit=dev --no-audit --no-fund && \
    npm uninstall puppeteer pdf2pic gm && \
    npm install html-pdf-node@1.0.8 jspdf@2.5.1 canvas@2.11.2 --save && \
    npm cache clean --force && \
    rm -rf /root/.npm

# Production stage
FROM node:20-alpine AS production

# Install only essential runtime dependencies
RUN apk add --no-cache \
    dumb-init \
    cairo \
    pango \
    libjpeg-turbo \
    giflib \
    && rm -rf /var/cache/apk/* \
    && rm -rf /tmp/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Set environment variables
ENV NODE_ENV=production

# Set working directory
WORKDIR /app

# Copy node_modules from builder stage
COPY --from=builder --chown=nodejs:nodejs /app/node_modules ./node_modules

# Copy application source
COPY --chown=nodejs:nodejs src ./src
COPY --chown=nodejs:nodejs public ./public
COPY --chown=nodejs:nodejs .env.production ./.env

# Switch to non-root user
USER nodejs

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "src/index.js"]
