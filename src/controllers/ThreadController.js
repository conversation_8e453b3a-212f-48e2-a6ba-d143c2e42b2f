import { ThreadService  } from '../services/ThreadService.js';
import { ResponseUtil  } from '../utils/response.js';
import { ERROR_MESSAGES, SUCCESS_MESSAGES  } from '../utils/constants.js';
import { asyncHandler  } from '../middleware/errorHandler.js';
import logger from '../config/logger.js';
import { FileProcessingService  } from '../services/FileProcessingService.js';

import { ChatThread  } from '../models/chat/index.js';
import { PineconeService  } from '../services/PineconeService.js';
import { ChatMessage  } from '../models/chat/index.js';

/**
 * Thread Controller
 * Handles thread operations including messaging and file attachments
 */
class ThreadController {
  /**
   * Send message in a thread (regular or project)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static sendMessage = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const chatRequest = req.body;
    const result = await ThreadService.processThreadChat(req.user.userId, chatRequest);
    
    ResponseUtil.success(res, SUCCESS_MESSAGES.MESSAGE_SENT, result);
  });

  /**
   * Send streaming message in a thread (regular or project)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static sendStreamingMessage = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const chatRequest = req.body;

    try {
      const result = await ThreadService.processThreadChatStreaming(req.user.userId, chatRequest, res);
      // Note: Response is handled by the streaming service, no need to send additional response
    } catch (error) {
      logger.error('Error in streaming thread chat:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });

  /**
   * Get user threads (regular threads only, not project threads)
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getUserThreads = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const limit = parseInt(req.query.limit) || 6;
    const offset = parseInt(req.query.offset) || 0;

    const threads = await ThreadService.getUserThreads(req.user.userId, limit, offset);
    
    ResponseUtil.success(res, 'Threads retrieved successfully', {
      threads,
      limit,
      offset,
    });
  });

  /**
   * Get thread messages
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getThreadMessages = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { threadId } = req.params;
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    if (!threadId) {
      ResponseUtil.badRequest(res, 'Thread ID is required');
      return;
    }

    const messages = await ThreadService.getThreadMessages(
      threadId,
      req.user.userId,
      limit,
      offset
    );

    ResponseUtil.success(res, 'Messages retrieved successfully', {
      messages,
      threadId,
      limit,
      offset,
    });
  });

  /**
   * Update thread name
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static updateThreadName = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { threadId } = req.params;
    const { name } = req.body;

    if (!threadId) {
      ResponseUtil.badRequest(res, 'Thread ID is required');
      return;
    }

    if (!name || name.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Thread name is required');
      return;
    }

    if (name.length > 255) {
      ResponseUtil.badRequest(res, 'Thread name is too long (max 255 characters)');
      return;
    }

    await ThreadService.updateThreadName(threadId, req.user.userId, name.trim());
    
    ResponseUtil.success(res, 'Thread name updated successfully');
  });

  /**
   * Delete thread
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static deleteThread = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { threadId } = req.params;

    if (!threadId) {
      ResponseUtil.badRequest(res, 'Thread ID is required');
      return;
    }

    // Find and verify thread ownership
    const thread = await ChatThread.findByIdAndUser(threadId, req.user.userId);
    
    if (!thread) {
      ResponseUtil.notFound(res, 'Thread not found');
      return;
    }

    // If it's a project thread, delete from Pinecone
    if (thread.projectId) {
      await PineconeService.deleteThreadMessages(thread.projectId, threadId);
    }

    // Delete thread and associated messages
    await thread.destroy();

    logger.info(`Thread deleted: ${threadId} by user ${req.user.userId}`);
    
    ResponseUtil.success(res, 'Thread deleted successfully');
  });

  /**
   * Get thread details
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static getThread = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { threadId } = req.params;

    if (!threadId) {
      ResponseUtil.badRequest(res, 'Thread ID is required');
      return;
    }

    const thread = await ChatThread.findByIdAndUser(threadId, req.user.userId);
    
    if (!thread) {
      ResponseUtil.notFound(res, 'Thread not found');
      return;
    }

    // Get message count
    const messageCount = await ChatMessage.count({
      where: { chatId: threadId }
    });

    const threadData = {
      ...thread.toJSON(),
      messageCount,
    };

    ResponseUtil.success(res, 'Thread retrieved successfully', threadData);
  });

  /**
   * Send streaming message in a thread with file attachment support
   * @param {Object} req - Express request object (AuthenticatedRequest)
   * @param {Object} res - Express response object
   */
  static sendStreamingMessageWithAttachment = asyncHandler(async (req, res) => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const chatRequest = req.body;
    const file = req.file;

    try {
      let processedFile = undefined;

      // Process file if attached
      if (file) {
        const fileAttachment = {
          buffer: file.buffer,
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size
        };

        // For project threads, we don't use session-based storage yet
        processedFile = await FileProcessingService.processFile(fileAttachment);
        logger.info(`File processed successfully: ${file.originalname} (${file.mimetype})`);
      }

      const result = await ThreadService.processThreadChatStreamingWithAttachment(
          req.user.userId,
          chatRequest,
          res,
          processedFile
      );
      // Note: Response is handled by the streaming service, no need to send additional response
    } catch (error) {
      logger.error('Error in streaming thread chat with attachment:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });
}

export { ThreadController  };
