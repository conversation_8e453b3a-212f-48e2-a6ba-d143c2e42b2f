#!/usr/bin/env node

/**
 * Test script to verify optimized services functionality
 * Tests invoice generation and file processing without breaking existing features
 */

import fs from 'fs/promises';
import path from 'path';

// Mock data for testing
const mockTransactionData = {
  id: 'test-transaction-123',
  userId: 'test-user-456',
  amount: '29.99',
  currency: 'USD',
  transactionType: 'SUBSCRIPTION',
  paymentMethod: 'Credit Card',
  paidAt: new Date().toISOString(),
  createdAt: new Date().toISOString(),
  invoiceNumber: 'INV-TEST-001',
  user: {
    email: '<EMAIL>',
    profile: {
      firstName: 'John',
      lastName: 'Doe'
    }
  }
};

const mockFileData = {
  buffer: Buffer.from('This is a test file content for processing.'),
  originalname: 'test-document.txt',
  mimetype: 'text/plain',
  size: 42
};

async function testInvoiceGeneration() {
  console.log('🧾 Testing Invoice Generation...');
  
  try {
    // Check if optimized service exists
    const servicePath = path.join(process.cwd(), 'src/services/HtmlInvoiceService.js');
    await fs.access(servicePath);
    
    // Dynamic import to test the service
    const { HtmlInvoiceService } = await import('./src/services/HtmlInvoiceService.js');
    
    console.log('   ✅ HtmlInvoiceService loaded successfully');
    
    // Test HTML generation
    const html = await HtmlInvoiceService.generateInvoiceHTML(mockTransactionData);
    if (html && html.includes('TheInfini AI')) {
      console.log('   ✅ HTML generation works');
    } else {
      console.log('   ❌ HTML generation failed');
      return false;
    }
    
    // Test template data preparation
    const templateData = await HtmlInvoiceService.prepareTemplateData(mockTransactionData);
    if (templateData && templateData.invoiceNumber) {
      console.log('   ✅ Template data preparation works');
    } else {
      console.log('   ❌ Template data preparation failed');
      return false;
    }
    
    console.log('   ✅ Invoice generation tests passed');
    return true;
    
  } catch (error) {
    console.log(`   ❌ Invoice generation test failed: ${error.message}`);
    return false;
  }
}

async function testFileProcessing() {
  console.log('📁 Testing File Processing...');
  
  try {
    // Check if optimized service exists
    const servicePath = path.join(process.cwd(), 'src/services/FileProcessingService.js');
    await fs.access(servicePath);
    
    console.log('   ✅ FileProcessingService file exists');
    
    // Test service loading (syntax check)
    try {
      const { FileProcessingService } = await import('./src/services/FileProcessingService.js');
      console.log('   ✅ FileProcessingService loaded successfully');
      
      // Test supported file types
      if (FileProcessingService.SUPPORTED_IMAGE_TYPES && 
          FileProcessingService.SUPPORTED_DOCUMENT_TYPES) {
        console.log('   ✅ File type constants defined');
      } else {
        console.log('   ❌ File type constants missing');
        return false;
      }
      
      console.log('   ✅ File processing tests passed');
      return true;
      
    } catch (importError) {
      console.log(`   ❌ Service import failed: ${importError.message}`);
      return false;
    }
    
  } catch (error) {
    console.log(`   ❌ File processing test failed: ${error.message}`);
    return false;
  }
}

async function testDependencies() {
  console.log('📦 Testing Dependencies...');
  
  const requiredDependencies = [
    'html-pdf-node',
    'jspdf',
    'canvas',
    'pdfjs-dist',
    'handlebars',
    'sharp',
    'mammoth',
    'xlsx'
  ];
  
  const removedDependencies = [
    'puppeteer',
    'pdf2pic',
    'gm'
  ];
  
  try {
    // Read package.json
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    const packageJsonContent = await fs.readFile(packageJsonPath, 'utf-8');
    const packageJson = JSON.parse(packageJsonContent);
    
    let allGood = true;
    
    // Check required dependencies
    console.log('   🔍 Checking required dependencies...');
    for (const dep of requiredDependencies) {
      if (packageJson.dependencies[dep]) {
        console.log(`   ✅ ${dep} is present`);
      } else {
        console.log(`   ❌ ${dep} is missing`);
        allGood = false;
      }
    }
    
    // Check removed dependencies
    console.log('   🔍 Checking removed dependencies...');
    for (const dep of removedDependencies) {
      if (!packageJson.dependencies[dep]) {
        console.log(`   ✅ ${dep} removed successfully`);
      } else {
        console.log(`   ⚠️  ${dep} still present (should be removed)`);
      }
    }
    
    return allGood;
    
  } catch (error) {
    console.log(`   ❌ Dependency test failed: ${error.message}`);
    return false;
  }
}

async function testDockerfile() {
  console.log('🐳 Testing Dockerfile...');
  
  try {
    const dockerfilePath = path.join(process.cwd(), 'Dockerfile');
    const dockerfileContent = await fs.readFile(dockerfilePath, 'utf-8');
    
    // Check for optimizations
    const checks = [
      { pattern: /FROM node:20-alpine/, name: 'Alpine base image' },
      { pattern: /AS builder/, name: 'Multi-stage build' },
      { pattern: /AS production/, name: 'Production stage' },
      { pattern: /dumb-init/, name: 'Process manager' },
      { pattern: /nodejs:nodejs/, name: 'Non-root user' }
    ];
    
    let allGood = true;
    
    for (const check of checks) {
      if (check.pattern.test(dockerfileContent)) {
        console.log(`   ✅ ${check.name} present`);
      } else {
        console.log(`   ❌ ${check.name} missing`);
        allGood = false;
      }
    }
    
    // Check that heavy dependencies are not installed
    if (!dockerfileContent.includes('graphicsmagick') || 
        !dockerfileContent.includes('ghostscript')) {
      console.log('   ✅ Heavy dependencies removed from Dockerfile');
    } else {
      console.log('   ⚠️  Heavy dependencies still in Dockerfile');
    }
    
    return allGood;
    
  } catch (error) {
    console.log(`   ❌ Dockerfile test failed: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🧪 Running Optimized Functionality Tests\n');
  
  const results = {
    invoice: await testInvoiceGeneration(),
    fileProcessing: await testFileProcessing(),
    dependencies: await testDependencies(),
    dockerfile: await testDockerfile()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.charAt(0).toUpperCase() + test.slice(1)}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  
  console.log('\n' + '='.repeat(50));
  
  if (allPassed) {
    console.log('🎉 All tests passed! The optimization is ready for deployment.');
    console.log('\n📋 Next steps:');
    console.log('   1. Run: npm install');
    console.log('   2. Build: docker build -t theinfini-ai-backend:optimized .');
    console.log('   3. Test: docker run -p 3000:3000 theinfini-ai-backend:optimized');
  } else {
    console.log('⚠️  Some tests failed. Please review the issues above.');
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure all optimized files are in place');
    console.log('   2. Run the migration script: ./migrate-to-optimized.sh');
    console.log('   3. Install dependencies: npm install');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run tests
runAllTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
