#!/bin/bash

# Docker build optimization script
# This script builds the optimized Docker image and shows size comparison

echo "🚀 Building optimized Docker image..."

# Build the optimized image
docker build -t theinfini-ai-backend:optimized .

# Show image sizes
echo ""
echo "📊 Image size comparison:"
echo "========================"

# Get the size of the new image
NEW_SIZE=$(docker images theinfini-ai-backend:optimized --format "table {{.Size}}" | tail -n 1)
echo "New optimized image: $NEW_SIZE"

# If there's an old image, show comparison
if docker images theinfini-ai-backend:latest >/dev/null 2>&1; then
    OLD_SIZE=$(docker images theinfini-ai-backend:latest --format "table {{.Size}}" | tail -n 1)
    echo "Previous image: $OLD_SIZE"
fi

echo ""
echo "🔍 Image layers breakdown:"
echo "========================="
docker history theinfini-ai-backend:optimized --format "table {{.CreatedBy}}\t{{.Size}}" | head -20

echo ""
echo "✅ Build complete! To run the container:"
echo "docker run -p 3000:3000 theinfini-ai-backend:optimized"

echo ""
echo "💡 Additional optimization tips:"
echo "- Consider using distroless base image for even smaller size"
echo "- Remove unused dependencies from package.json"
echo "- Use .dockerignore to exclude more files"
echo "- Consider using Alpine-based images for dependencies"
