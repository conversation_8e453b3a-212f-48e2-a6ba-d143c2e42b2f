#!/bin/bash

# Migration script to replace services with optimized versions
# This script safely backs up original files and replaces them with optimized versions

set -e  # Exit on any error

echo "🚀 Starting migration to optimized services..."

# Create backup directory
BACKUP_DIR="./backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📁 Creating backup in $BACKUP_DIR..."

# Backup original files
if [ -f "src/services/HtmlInvoiceService.js" ]; then
    cp "src/services/HtmlInvoiceService.js" "$BACKUP_DIR/"
    echo "   ✅ Backed up HtmlInvoiceService.js"
fi

if [ -f "src/services/FileProcessingService.js" ]; then
    cp "src/services/FileProcessingService.js" "$BACKUP_DIR/"
    echo "   ✅ Backed up FileProcessingService.js"
fi

if [ -f "package.json" ]; then
    cp "package.json" "$BACKUP_DIR/"
    echo "   ✅ Backed up package.json"
fi

if [ -f "Dockerfile" ]; then
    cp "Dockerfile" "$BACKUP_DIR/Dockerfile.original"
    echo "   ✅ Backed up Dockerfile"
fi

echo ""
echo "🔄 Replacing services with optimized versions..."

# Replace HtmlInvoiceService
if [ -f "src/services/HtmlInvoiceService.optimized.js" ]; then
    cp "src/services/HtmlInvoiceService.optimized.js" "src/services/HtmlInvoiceService.js"
    echo "   ✅ Replaced HtmlInvoiceService.js"
else
    echo "   ⚠️  HtmlInvoiceService.optimized.js not found"
fi

# Replace FileProcessingService
if [ -f "src/services/FileProcessingService.optimized.js" ]; then
    cp "src/services/FileProcessingService.optimized.js" "src/services/FileProcessingService.js"
    echo "   ✅ Replaced FileProcessingService.js"
else
    echo "   ⚠️  FileProcessingService.optimized.js not found"
fi

# Replace Dockerfile
if [ -f "Dockerfile.optimized-functional" ]; then
    cp "Dockerfile.optimized-functional" "Dockerfile"
    echo "   ✅ Replaced Dockerfile"
else
    echo "   ⚠️  Dockerfile.optimized-functional not found"
fi

echo ""
echo "📦 Updating dependencies..."

# Update package.json
if [ -f "update-dependencies.js" ]; then
    node update-dependencies.js
else
    echo "   ⚠️  update-dependencies.js not found, updating manually..."
    
    # Manual dependency updates
    echo "   🔄 Please run the following commands manually:"
    echo "      npm uninstall puppeteer pdf2pic gm"
    echo "      npm install html-pdf-node@1.0.8 jspdf@2.5.1 canvas@2.11.2 pdfjs-dist@4.0.379 pdf-parse@1.1.1"
fi

echo ""
echo "🧪 Testing the migration..."

# Check if Node.js can load the new services
echo "   🔍 Checking service syntax..."

if node -c "src/services/HtmlInvoiceService.js" 2>/dev/null; then
    echo "   ✅ HtmlInvoiceService.js syntax OK"
else
    echo "   ❌ HtmlInvoiceService.js has syntax errors"
fi

if node -c "src/services/FileProcessingService.js" 2>/dev/null; then
    echo "   ✅ FileProcessingService.js syntax OK"
else
    echo "   ❌ FileProcessingService.js has syntax errors"
fi

echo ""
echo "🐳 Building optimized Docker image..."

# Build the optimized Docker image
if docker build -t theinfini-ai-backend:optimized . > /dev/null 2>&1; then
    echo "   ✅ Docker image built successfully"
    
    # Show image size
    IMAGE_SIZE=$(docker images theinfini-ai-backend:optimized --format "{{.Size}}" | head -1)
    echo "   📊 New image size: $IMAGE_SIZE"
else
    echo "   ❌ Docker build failed"
    echo "   🔄 Check the build logs with: docker build -t theinfini-ai-backend:optimized ."
fi

echo ""
echo "✅ Migration completed!"
echo ""
echo "📋 Summary:"
echo "   • Original files backed up to: $BACKUP_DIR"
echo "   • Services replaced with optimized versions"
echo "   • Dependencies updated to lighter alternatives"
echo "   • Docker image rebuilt"
echo ""
echo "🧪 Next steps:"
echo "   1. Test the application: docker run -p 3000:3000 theinfini-ai-backend:optimized"
echo "   2. Verify invoice generation works"
echo "   3. Test file processing functionality"
echo "   4. If issues occur, restore from backup: cp $BACKUP_DIR/* src/services/"
echo ""
echo "🔧 Rollback command (if needed):"
echo "   cp $BACKUP_DIR/HtmlInvoiceService.js src/services/"
echo "   cp $BACKUP_DIR/FileProcessingService.js src/services/"
echo "   cp $BACKUP_DIR/package.json ."
echo "   cp $BACKUP_DIR/Dockerfile.original Dockerfile"
