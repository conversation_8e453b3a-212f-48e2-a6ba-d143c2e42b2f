#!/usr/bin/env node

/**
 * Script to update package.json with optimized dependencies
 * Replaces heavy dependencies with lighter alternatives
 */

import fs from 'fs/promises';
import path from 'path';

async function updatePackageJson() {
  try {
    console.log('🔄 Updating package.json with optimized dependencies...');

    // Read current package.json
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    const packageJsonContent = await fs.readFile(packageJsonPath, 'utf-8');
    const packageJson = JSON.parse(packageJsonContent);

    // Remove heavy dependencies
    const dependenciesToRemove = [
      'puppeteer',
      'pdf2pic',
      'gm'
    ];

    console.log('❌ Removing heavy dependencies:');
    dependenciesToRemove.forEach(dep => {
      if (packageJson.dependencies[dep]) {
        console.log(`   - ${dep}`);
        delete packageJson.dependencies[dep];
      }
    });

    // Add lighter alternatives
    const newDependencies = {
      'html-pdf-node': '^1.0.8',
      'jspdf': '^2.5.1',
      'canvas': '^2.11.2',
      'pdfjs-dist': '^4.0.379',
      'pdf-parse': '^1.1.1'
    };

    console.log('✅ Adding lightweight alternatives:');
    Object.entries(newDependencies).forEach(([dep, version]) => {
      console.log(`   + ${dep}@${version}`);
      packageJson.dependencies[dep] = version;
    });

    // Sort dependencies alphabetically
    const sortedDependencies = {};
    Object.keys(packageJson.dependencies)
      .sort()
      .forEach(key => {
        sortedDependencies[key] = packageJson.dependencies[key];
      });
    packageJson.dependencies = sortedDependencies;

    // Write updated package.json
    await fs.writeFile(
      packageJsonPath, 
      JSON.stringify(packageJson, null, 2) + '\n',
      'utf-8'
    );

    console.log('✅ package.json updated successfully!');
    console.log('\n📦 Next steps:');
    console.log('   1. Run: npm install');
    console.log('   2. Replace service files with optimized versions');
    console.log('   3. Build Docker image with: docker build -f Dockerfile.optimized-functional -t theinfini-ai-backend:optimized .');

  } catch (error) {
    console.error('❌ Error updating package.json:', error);
    process.exit(1);
  }
}

// Run the update
updatePackageJson();
